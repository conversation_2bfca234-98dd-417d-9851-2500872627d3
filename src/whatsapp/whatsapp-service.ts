import makeWASocket, {
  DisconnectReason,
  useMultiFileAuthState,
  WASocket,
  ConnectionState,
  BaileysEventMap,
  proto,
} from 'baileys';
import { Boom } from '@hapi/boom';
import qrcode from 'qrcode-terminal';
import QRCode from 'qrcode';
import { EventEmitter } from 'events';

export interface WhatsAppMessage {
  id: string;
  from: string;
  to: string;
  message: string;
  timestamp: Date;
  messageType: string;
  isFromMe: boolean;
}

export class WhatsAppService extends EventEmitter {
  private socket: WASocket | null = null;
  private qrCode: string | null = null;
  private connectionState: ConnectionState = 'close';
  private isConnected = false;

  constructor() {
    super();
  }

  async initialize(): Promise<void> {
    try {
      const { state, saveCreds } = await useMultiFileAuthState('./auth_info_baileys');
      
      this.socket = makeWASocket({
        auth: state,
        printQRInTerminal: false, // We'll handle QR display ourselves
        logger: {
          level: 'silent', // Reduce noise in logs
          child: () => ({ level: 'silent' } as any),
          trace: () => {},
          debug: () => {},
          info: () => {},
          warn: () => {},
          error: () => {},
          fatal: () => {},
        },
      });

      this.socket.ev.on('connection.update', async (update) => {
        const { connection, lastDisconnect, qr } = update;
        
        if (qr) {
          this.qrCode = await QRCode.toDataURL(qr);
          qrcode.generate(qr, { small: true });
          console.log('QR Code generated. Please scan with WhatsApp.');
          this.emit('qr', this.qrCode);
        }

        if (connection === 'close') {
          this.isConnected = false;
          this.connectionState = 'close';
          const shouldReconnect = (lastDisconnect?.error as Boom)?.output?.statusCode !== DisconnectReason.loggedOut;
          
          console.log('Connection closed due to:', lastDisconnect?.error);
          
          if (shouldReconnect) {
            console.log('Reconnecting...');
            setTimeout(() => this.initialize(), 3000);
          } else {
            console.log('Logged out. Please scan QR code again.');
            this.emit('logged-out');
          }
        } else if (connection === 'open') {
          this.isConnected = true;
          this.connectionState = 'open';
          this.qrCode = null;
          console.log('WhatsApp connected successfully!');
          this.emit('connected');
        }
      });

      this.socket.ev.on('creds.update', saveCreds);

      this.socket.ev.on('messages.upsert', async (m) => {
        const messages = m.messages;
        
        for (const message of messages) {
          if (message.key && message.message) {
            const whatsappMessage = this.parseMessage(message);
            if (whatsappMessage) {
              console.log('Received message:', whatsappMessage);
              this.emit('message', whatsappMessage);
            }
          }
        }
      });

    } catch (error) {
      console.error('Failed to initialize WhatsApp service:', error);
      this.emit('error', error);
    }
  }

  private parseMessage(message: proto.IWebMessageInfo): WhatsAppMessage | null {
    try {
      const messageId = message.key?.id || '';
      const from = message.key?.remoteJid || '';
      const isFromMe = message.key?.fromMe || false;
      const timestamp = new Date((message.messageTimestamp as number) * 1000);

      let messageText = '';
      let messageType = 'unknown';

      if (message.message?.conversation) {
        messageText = message.message.conversation;
        messageType = 'text';
      } else if (message.message?.extendedTextMessage?.text) {
        messageText = message.message.extendedTextMessage.text;
        messageType = 'text';
      } else if (message.message?.imageMessage?.caption) {
        messageText = message.message.imageMessage.caption;
        messageType = 'image';
      } else if (message.message?.videoMessage?.caption) {
        messageText = message.message.videoMessage.caption;
        messageType = 'video';
      } else if (message.message?.documentMessage?.caption) {
        messageText = message.message.documentMessage.caption;
        messageType = 'document';
      } else if (message.message?.audioMessage) {
        messageText = '[Audio Message]';
        messageType = 'audio';
      } else if (message.message?.stickerMessage) {
        messageText = '[Sticker]';
        messageType = 'sticker';
      }

      if (!messageText && !isFromMe) {
        return null; // Skip empty messages that aren't from us
      }

      return {
        id: messageId,
        from,
        to: from, // In WhatsApp, 'to' is usually the same as 'from' for incoming messages
        message: messageText,
        timestamp,
        messageType,
        isFromMe,
      };
    } catch (error) {
      console.error('Error parsing message:', error);
      return null;
    }
  }

  async sendMessage(to: string, message: string): Promise<void> {
    if (!this.socket || !this.isConnected) {
      throw new Error('WhatsApp is not connected');
    }

    try {
      await this.socket.sendMessage(to, { text: message });
      console.log(`Message sent to ${to}: ${message}`);
    } catch (error) {
      console.error('Failed to send message:', error);
      throw error;
    }
  }

  getQRCode(): string | null {
    return this.qrCode;
  }

  getConnectionState(): ConnectionState {
    return this.connectionState;
  }

  isWhatsAppConnected(): boolean {
    return this.isConnected;
  }

  async disconnect(): Promise<void> {
    if (this.socket) {
      await this.socket.logout();
      this.socket = null;
      this.isConnected = false;
      this.connectionState = 'close';
    }
  }
}
