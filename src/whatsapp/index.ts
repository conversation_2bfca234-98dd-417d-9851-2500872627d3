import { WhatsAppServer } from './server.js';

const PORT = process.env.WHATSAPP_PORT ? parseInt(process.env.WHATSAPP_PORT) : 3000;

async function startWhatsAppServer() {
  try {
    const server = new WhatsAppServer(PORT);
    await server.start();

    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      console.log('\n🛑 Shutting down WhatsApp server...');
      await server.stop();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      console.log('\n🛑 Shutting down WhatsApp server...');
      await server.stop();
      process.exit(0);
    });

  } catch (error) {
    console.error('❌ Failed to start WhatsApp server:', error);
    process.exit(1);
  }
}

// Start the server if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  startWhatsAppServer();
}

export { WhatsAppServer } from './server.js';
export { WhatsAppService } from './whatsapp-service.js';
export type { WhatsAppMessage } from './whatsapp-service.js';
