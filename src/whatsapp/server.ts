import express from 'express';
import { WhatsAppService, WhatsAppMessage } from './whatsapp-service.js';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export class WhatsAppServer {
  private app: express.Application;
  private whatsappService: WhatsAppService;
  private server: any;
  private port: number;

  constructor(port: number = 3000) {
    this.app = express();
    this.whatsappService = new WhatsAppService();
    this.port = port;
    this.setupMiddleware();
    this.setupRoutes();
    this.setupWhatsAppEvents();
  }

  private setupMiddleware(): void {
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
    this.app.use(express.static(path.join(__dirname, 'public')));
  }

  private setupRoutes(): void {
    // Serve the setup page
    this.app.get('/', (req, res) => {
      res.send(this.getSetupPageHTML());
    });

    // API endpoint to get QR code
    this.app.get('/api/qr', (req, res) => {
      const qrCode = this.whatsappService.getQRCode();
      const connectionState = this.whatsappService.getConnectionState();
      const isConnected = this.whatsappService.isWhatsAppConnected();

      res.json({
        qrCode,
        connectionState,
        isConnected,
      });
    });

    // API endpoint to get connection status
    this.app.get('/api/status', (req, res) => {
      res.json({
        connectionState: this.whatsappService.getConnectionState(),
        isConnected: this.whatsappService.isWhatsAppConnected(),
      });
    });

    // API endpoint to send a message (for testing)
    this.app.post('/api/send', async (req, res) => {
      try {
        const { to, message } = req.body;
        
        if (!to || !message) {
          return res.status(400).json({ error: 'Missing "to" or "message" field' });
        }

        await this.whatsappService.sendMessage(to, message);
        res.json({ success: true, message: 'Message sent successfully' });
      } catch (error) {
        console.error('Error sending message:', error);
        res.status(500).json({ error: 'Failed to send message' });
      }
    });

    // API endpoint to disconnect WhatsApp
    this.app.post('/api/disconnect', async (req, res) => {
      try {
        await this.whatsappService.disconnect();
        res.json({ success: true, message: 'Disconnected successfully' });
      } catch (error) {
        console.error('Error disconnecting:', error);
        res.status(500).json({ error: 'Failed to disconnect' });
      }
    });

    // API endpoint to reconnect WhatsApp
    this.app.post('/api/connect', async (req, res) => {
      try {
        await this.whatsappService.initialize();
        res.json({ success: true, message: 'Connection initiated' });
      } catch (error) {
        console.error('Error connecting:', error);
        res.status(500).json({ error: 'Failed to connect' });
      }
    });
  }

  private setupWhatsAppEvents(): void {
    this.whatsappService.on('message', (message: WhatsAppMessage) => {
      console.log('📱 WhatsApp Message Received:');
      console.log(`From: ${message.from}`);
      console.log(`Type: ${message.messageType}`);
      console.log(`Message: ${message.message}`);
      console.log(`Time: ${message.timestamp.toISOString()}`);
      console.log(`Is from me: ${message.isFromMe}`);
      console.log('---');
    });

    this.whatsappService.on('connected', () => {
      console.log('✅ WhatsApp connected successfully!');
    });

    this.whatsappService.on('qr', (qrCode: string) => {
      console.log('📱 QR Code generated for WhatsApp setup');
    });

    this.whatsappService.on('logged-out', () => {
      console.log('❌ WhatsApp logged out');
    });

    this.whatsappService.on('error', (error: Error) => {
      console.error('❌ WhatsApp error:', error);
    });
  }

  private getSetupPageHTML(): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Bot Setup</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
            font-weight: bold;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.connecting {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .qr-container {
            text-align: center;
            margin: 20px 0;
        }
        .qr-code {
            max-width: 300px;
            margin: 20px auto;
            display: block;
        }
        .instructions {
            background-color: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button.danger {
            background-color: #dc3545;
        }
        .button.danger:hover {
            background-color: #c82333;
        }
        .hidden {
            display: none;
        }
        .test-section {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 1px solid #dee2e6;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 WhatsApp Bot Setup</h1>
            <p>Connect your WhatsApp account to start receiving messages</p>
        </div>

        <div id="status" class="status disconnected">
            Disconnected
        </div>

        <div id="qr-section" class="qr-container hidden">
            <h3>Scan QR Code with WhatsApp</h3>
            <img id="qr-code" class="qr-code" src="" alt="QR Code">
            <div class="instructions">
                <h4>How to connect:</h4>
                <ol>
                    <li>Open WhatsApp on your phone</li>
                    <li>Go to Settings → Linked Devices</li>
                    <li>Tap "Link a Device"</li>
                    <li>Scan the QR code above</li>
                </ol>
            </div>
        </div>

        <div class="text-center">
            <button id="connect-btn" class="button" onclick="connect()">Connect WhatsApp</button>
            <button id="disconnect-btn" class="button danger hidden" onclick="disconnect()">Disconnect</button>
        </div>

        <div id="test-section" class="test-section hidden">
            <h3>Test Message Sending</h3>
            <div class="form-group">
                <label for="phone-number">Phone Number (with country code, e.g., <EMAIL>):</label>
                <input type="text" id="phone-number" placeholder="<EMAIL>">
            </div>
            <div class="form-group">
                <label for="test-message">Message:</label>
                <textarea id="test-message" rows="3" placeholder="Enter your test message here..."></textarea>
            </div>
            <button class="button" onclick="sendTestMessage()">Send Test Message</button>
        </div>
    </div>

    <script>
        let statusCheckInterval;

        function updateStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    const statusEl = document.getElementById('status');
                    const qrSection = document.getElementById('qr-section');
                    const connectBtn = document.getElementById('connect-btn');
                    const disconnectBtn = document.getElementById('disconnect-btn');
                    const testSection = document.getElementById('test-section');

                    if (data.isConnected) {
                        statusEl.textContent = 'Connected ✅';
                        statusEl.className = 'status connected';
                        qrSection.classList.add('hidden');
                        connectBtn.classList.add('hidden');
                        disconnectBtn.classList.remove('hidden');
                        testSection.classList.remove('hidden');
                    } else if (data.connectionState === 'connecting') {
                        statusEl.textContent = 'Connecting... ⏳';
                        statusEl.className = 'status connecting';
                        checkForQR();
                    } else {
                        statusEl.textContent = 'Disconnected ❌';
                        statusEl.className = 'status disconnected';
                        qrSection.classList.add('hidden');
                        connectBtn.classList.remove('hidden');
                        disconnectBtn.classList.add('hidden');
                        testSection.classList.add('hidden');
                    }
                })
                .catch(error => console.error('Error checking status:', error));
        }

        function checkForQR() {
            fetch('/api/qr')
                .then(response => response.json())
                .then(data => {
                    if (data.qrCode) {
                        document.getElementById('qr-code').src = data.qrCode;
                        document.getElementById('qr-section').classList.remove('hidden');
                    }
                })
                .catch(error => console.error('Error getting QR code:', error));
        }

        function connect() {
            fetch('/api/connect', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    console.log('Connection initiated:', data);
                    updateStatus();
                })
                .catch(error => console.error('Error connecting:', error));
        }

        function disconnect() {
            fetch('/api/disconnect', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    console.log('Disconnected:', data);
                    updateStatus();
                })
                .catch(error => console.error('Error disconnecting:', error));
        }

        function sendTestMessage() {
            const phoneNumber = document.getElementById('phone-number').value;
            const message = document.getElementById('test-message').value;

            if (!phoneNumber || !message) {
                alert('Please fill in both phone number and message');
                return;
            }

            fetch('/api/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    to: phoneNumber,
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Message sent successfully!');
                    document.getElementById('test-message').value = '';
                } else {
                    alert('Failed to send message: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error sending message:', error);
                alert('Error sending message');
            });
        }

        // Start status checking
        updateStatus();
        statusCheckInterval = setInterval(updateStatus, 2000);
    </script>
</body>
</html>
    `;
  }

  async start(): Promise<void> {
    // Initialize WhatsApp service
    await this.whatsappService.initialize();

    // Start Express server
    this.server = this.app.listen(this.port, () => {
      console.log(`🚀 WhatsApp Server running on http://localhost:${this.port}`);
      console.log(`📱 Open http://localhost:${this.port} to set up WhatsApp connection`);
    });
  }

  async stop(): Promise<void> {
    if (this.server) {
      this.server.close();
    }
    await this.whatsappService.disconnect();
  }
}
