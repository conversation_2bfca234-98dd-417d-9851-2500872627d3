#!/usr/bin/env tsx

import { WhatsAppServer } from './src/whatsapp/index.js';

const PORT = process.env.WHATSAPP_PORT ? parseInt(process.env.WHATSAPP_PORT) : 3000;

async function main() {
  console.log('🚀 Starting WhatsApp Bot Server...');
  
  const server = new WhatsAppServer(PORT);
  
  try {
    await server.start();
    
    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      console.log('\n🛑 Shutting down WhatsApp server...');
      await server.stop();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      console.log('\n🛑 Shutting down WhatsApp server...');
      await server.stop();
      process.exit(0);
    });

  } catch (error) {
    console.error('❌ Failed to start WhatsApp server:', error);
    process.exit(1);
  }
}

main().catch(console.error);
