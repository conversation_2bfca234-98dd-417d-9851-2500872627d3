{"name": "personal-ai-bot-v2", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "<PERSON>ra dev", "build": "mastra build", "start": "mastra start"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "engines": {"node": ">=20.9.0"}, "dependencies": {"@ai-sdk/google": "^1.2.22", "@mastra/core": "^0.12.1-alpha.0", "@mastra/fastembed": "^0.10.1", "@mastra/libsql": "^0.12.0", "@mastra/loggers": "^0.10.5", "@mastra/memory": "^0.12.0", "baileys": "^6.7.18", "zod": "^3.25.76"}, "devDependencies": {"@types/node": "^24.1.0", "mastra": "^0.10.17-alpha.0", "typescript": "^5.8.3"}, "trustedDependencies": ["baileys", "esbuild", "onnxruntime-node", "protobufjs"]}